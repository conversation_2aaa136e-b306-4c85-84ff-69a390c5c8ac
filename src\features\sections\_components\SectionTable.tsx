import * as React from 'react'
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { MoreHorizontalIcon, EditIcon, Trash2Icon, UsersIcon } from 'lucide-react'
import { useQuery } from 'convex/react'
import { Button } from '@/components/ui/Button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/Table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/DropdownMenu'
import { api } from 'convex/_generated/api'
import type { Section, SectionId } from '@/lib/types'

const columnHelper = createColumnHelper<Section>()

export function SectionTable({
  onEdit,
  onDelete,
}: {
  onEdit: (sectionId: SectionId) => void
  onDelete: (sectionId: SectionId) => void
}) {
  const sections = useQuery(api.sections.getAll)
  const teachers = useQuery(api.teachers.getAll)
  const tracks = useQuery(api.tracks.getAll)
  const strands = useQuery(api.strands.getAll)
  const majors = useQuery(api.majors.getAll)

  const columns = React.useMemo(
    () => [
      columnHelper.accessor('name', {
        header: 'Section Name',
        cell: (info) => <div className="font-medium">{info.getValue()}</div>,
      }),
      columnHelper.accessor('gradeLevel', {
        header: 'Grade Level',
        cell: (info) => <div className="text-sm">Grade {info.getValue()}</div>,
      }),
      columnHelper.accessor(
        (row) => {
          const teacher = teachers?.find((t) => t._id === row.adviserId)
          return teacher ? `${teacher.firstName} ${teacher.lastName}` : ''
        },
        {
          id: 'adviser',
          header: 'Adviser',
          cell: (info) => {
            const adviserName = info.getValue()
            return adviserName ? <div className="text-sm">{adviserName}</div> : null
          },
        }
      ),
      columnHelper.accessor(
        (row) => {
          const track = row.trackId ? tracks?.find((t) => t._id === row.trackId) : null
          return track?.name || ''
        },
        {
          id: 'track',
          header: 'Track',
          cell: (info) => {
            const trackName = info.getValue()
            return trackName ? <div className="text-sm">{trackName}</div> : null
          },
        }
      ),
      columnHelper.accessor(
        (row) => {
          const strand = row.strandId ? strands?.find((s) => s._id === row.strandId) : null
          return strand?.name || ''
        },
        {
          id: 'strand',
          header: 'Strand',
          cell: (info) => {
            const strandName = info.getValue()
            return strandName ? <div className="text-sm">{strandName}</div> : null
          },
        }
      ),
      columnHelper.accessor(
        (row) => {
          const major = row.majorId ? majors?.find((m) => m._id === row.majorId) : null
          return major?.name || ''
        },
        {
          id: 'major',
          header: 'Major',
          cell: (info) => {
            const majorName = info.getValue()
            return majorName ? <div className="text-sm">{majorName}</div> : null
          },
        }
      ),
      columnHelper.accessor((row) => row.maleCount + row.femaleCount, {
        id: 'enrollment',
        header: 'Enrollment',
        cell: (info) => {
          const section = info.row.original
          return (
            <div className="text-sm">
              <div className="font-medium">Total: {info.getValue()}</div>
              <div className="text-muted-foreground">
                Male: {section.maleCount} | Female: {section.femaleCount}
              </div>
            </div>
          )
        },
      }),
      columnHelper.display({
        id: 'actions',
        header: '',
        enableSorting: false,
        cell: (info) => {
          const section = info.row.original
          return (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="size-8">
                  <MoreHorizontalIcon />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(section._id)}>
                  <EditIcon />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDelete(section._id)} variant="destructive">
                  <Trash2Icon />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )
        },
      }),
    ],
    [teachers, tracks, strands, majors, onEdit, onDelete]
  )

  const table = useReactTable({
    data: sections ?? [],
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },
  })

  if (table.getRowModel().rows.length === 0) {
    return (
      <div className="p-8 text-center">
        <UsersIcon className="mx-auto text-muted-foreground" />
        <h3 className="mt-4 text-lg font-semibold">No sections found</h3>
        <p className="mt-2 text-sm text-muted-foreground">
          No sections match your current filters. Try adjusting your search criteria.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id} className="text-center">
                    {header.isPlaceholder ? null : (
                      <div className="flex justify-center">
                        {flexRender(header.column.columnDef.header, header.getContext())}
                      </div>
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id} className="text-center">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}{' '}
          to{' '}
          {Math.min(
            (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
            table.getRowModel().rows.length
          )}{' '}
          of {table.getRowModel().rows.length} results
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(table.getPageCount(), 5) }, (_, i) => {
              const pageIndex =
                table.getPageCount() <= 5
                  ? i
                  : Math.max(
                      0,
                      Math.min(
                        table.getState().pagination.pageIndex - 2 + i,
                        table.getPageCount() - 5 + i
                      )
                    )

              return (
                <Button
                  key={pageIndex}
                  variant={
                    table.getState().pagination.pageIndex === pageIndex ? 'default' : 'outline'
                  }
                  size="sm"
                  onClick={() => table.setPageIndex(pageIndex)}
                  className="size-7 p-0 rounded-md"
                >
                  {pageIndex + 1}
                </Button>
              )
            })}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  )
}
